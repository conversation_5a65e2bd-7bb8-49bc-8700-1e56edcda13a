"""
闲鱼API基础客户端
处理Cookie、签名生成、通用HTTP请求等基础功能
"""
import time
import json
from typing import Optional, Dict, Any
import requests
from requests.cookies import create_cookie
from loguru import logger

# 导入现有的工具函数
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from utils.xianyu_utils import generate_sign, trans_cookies



class BaseXianyuClient:
    """闲鱼API基础客户端"""
    
    def __init__(self, cookies_str: str):
        """
        初始化基础客户端
        
        Args:
            cookies_str: Cookie字符串
        """
        self.session = requests.Session()
        self.cookies_str = cookies_str
        self.cookies_dict = trans_cookies(cookies_str) if cookies_str else {}
        
        # 设置通用headers
        self.session.headers.update({
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'origin': 'https://www.goofish.com',
            'referer': 'https://www.goofish.com/',
            "content-type": "application/x-www-form-urlencoded",
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
            "cookie":cookies_str
        })
        
        # self._setup_cookies()
        logger.info("闲鱼基础客户端初始化完成")
    
    # def _setup_cookies(self):
    #     """设置Cookie"""
    #     if not self.cookies_dict:
    #         logger.warning("未提供有效的Cookie")
    #         return
    #
    #     # 关键Cookie字段
    #     cookie_fields = [
    #         "cna", "t", "sgcookie", "tracknick", "unb", "xlly_s",
    #         "cookie2", "_samesite_flag_", "_tb_token_", "mtop_partitioned_detect",
    #         "_m_h5_tk", "_m_h5_tk_enc", "tfstk"
    #     ]
    #
    #
    #
    #     for field in cookie_fields:
    #         if field in self.cookies_dict:
    #             self.session.cookies.set_cookie(create_cookie(
    #                 name=field,
    #                 value=self.cookies_dict[field],
    #                 domain=".goofish.com",
    #                 path="/"
    #             ))
    #
    #     logger.info("Cookie设置完成")
    
    def _get_token(self) -> Optional[str]:
        """获取token"""
        if not self.cookies_dict or '_m_h5_tk' not in self.cookies_dict:
            logger.error("Cookie中缺少_m_h5_tk字段")
            return None
        return self.cookies_dict['_m_h5_tk'].split('_')[0]
    
    def make_request(self, api: str, data_payload: dict, 
                    spm_pre: str = "",
                    log_id: str = "",
                    spm_cnt:str ="",
                    max_retry: int = 3) -> Optional[dict]:
        """
        发起API请求
        
        Args:
            api: API名称
            data_payload: 请求数据
            spm_pre: spm_pre参数
            log_id: log_id参数
            max_retry: 最大重试次数
            
        Returns:
            API响应数据
        """
        for attempt in range(max_retry):
            try:
                # 获取token
                token = self._get_token()
                if not token:
                    logger.error("无法获取token")
                    return None
                
                # 生成时间戳
                timestamp = str(int(time.time() * 1000))
                
                # 构建请求数据
                data_val = json.dumps(data_payload)
                
                # 生成签名
                sign = generate_sign(timestamp, token, data_val)
                
                # 构建请求参数
                params = {
                    "jsv": "2.7.2",
                    "appKey": "********",
                    "t": timestamp,
                    "sign": sign,
                    "v": "1.0",
                    "type": "originaljson",
                    "accountSite": "xianyu",
                    "dataType": "json",
                    "timeout": "20000",
                    "api": api,
                    "sessionOption": "AutoLoginOnly",
                    "spm_cnt": spm_cnt,
                    "spm_pre": spm_pre,
                    "log_id": log_id
                }
                # 构建请求体
                data = {"data": data_val}
                
                # 发送请求
                url = f"https://h5api.m.goofish.com/h5/{api}/1.0/"
                response = self.session.post(url, params=params, data=data, timeout=30)
                # 检查响应
                if response.status_code == 200:
                    result = response.json()
                    
                    # 检查API响应状态
                    if result.get('ret') and result['ret'][0] == 'SUCCESS::调用成功':
                        logger.debug(f"API调用成功: {api}")
                        return result
                    else:
                        logger.error(f"API调用失败1: {result.get('ret', ['未知错误'])}")
                        return None
                else:
                    logger.error(f"HTTP请求失败: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"请求异常 (尝试 {attempt + 1}/{max_retry}): {e}")
                if attempt < max_retry - 1:
                    time.sleep(1)  # 重试前等待1秒
                    continue
        
        logger.error(f"API请求失败，已达到最大重试次数: {api}")
        return None
    
    def test_connection(self) -> bool:
        """测试连接是否正常"""
        try:
            # 使用一个简单的API进行测试
            test_data = {"self": False, "userId": "2216036737826"}
            result = self.make_request("mtop.idle.web.user.page.head", test_data)
            return result is not None
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
