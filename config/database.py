#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置文件
包含MySQL连接配置和数据库相关设置

Author: AI Assistant
Date: 2025-07-30
"""

import os
from typing import Dict, Any

# ================================
# 数据库连接配置
# ================================

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': '***********',
    'port': 3309,
    'user': 'de_cfTnAG',
    'password': 'de_Bz6Sr4',
    'db': 'de_48eidc',
    'charset': 'utf8mb4',
    'autocommit': True,
    'connect_timeout': 10,
    'echo': False,  # 是否打印SQL语句
}

# 连接池配置
POOL_CONFIG = {
    'minsize': 1,           # 最小连接数
    'maxsize': 10,          # 最大连接数
    'pool_recycle': 3600,   # 连接回收时间(秒)
    'echo': False,          # 是否打印SQL
}

# 数据库表配置
TABLE_CONFIG = {
    'xianyu_users': {
        'name': 'xianyu_users',
        'comment': '闲鱼用户信息表',
        'engine': 'InnoDB',
        'charset': 'utf8mb4',
    }
}

# 环境变量覆盖配置
def get_database_config() -> Dict[str, Any]:
    """
    获取数据库配置，支持环境变量覆盖
    
    Returns:
        Dict[str, Any]: 数据库配置字典
    """
    config = MYSQL_CONFIG.copy()
    
    # 环境变量覆盖
    if os.getenv('MYSQL_HOST'):
        config['host'] = os.getenv('MYSQL_HOST')
    if os.getenv('MYSQL_PORT'):
        config['port'] = int(os.getenv('MYSQL_PORT'))
    if os.getenv('MYSQL_USER'):
        config['user'] = os.getenv('MYSQL_USER')
    if os.getenv('MYSQL_PASSWORD'):
        config['password'] = os.getenv('MYSQL_PASSWORD')
    if os.getenv('MYSQL_DATABASE'):
        config['db'] = os.getenv('MYSQL_DATABASE')
    
    return config

def get_pool_config() -> Dict[str, Any]:
    """
    获取连接池配置
    
    Returns:
        Dict[str, Any]: 连接池配置字典
    """
    return POOL_CONFIG.copy()

# 数据库URL构建
def get_database_url() -> str:
    """
    构建数据库连接URL
    
    Returns:
        str: 数据库连接URL
    """
    config = get_database_config()
    return f"mysql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['db']}"

# 测试连接配置
def validate_config() -> bool:
    """
    验证数据库配置是否完整
    
    Returns:
        bool: 配置是否有效
    """
    config = get_database_config()
    required_keys = ['host', 'port', 'user', 'password', 'db']
    
    for key in required_keys:
        if not config.get(key):
            return False
    
    return True

if __name__ == "__main__":
    # 配置验证测试
    print("数据库配置验证:")
    print(f"配置有效: {validate_config()}")
    print(f"数据库URL: {get_database_url()}")
    print(f"连接配置: {get_database_config()}")
