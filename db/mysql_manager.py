#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步MySQL数据库管理器
提供连接池管理、事务处理和基础CRUD操作

Author: AI Assistant
Date: 2025-07-30
"""

import aiomysql
import asyncio
from typing import Dict, List, Any, Optional, Union
from loguru import logger
from contextlib import asynccontextmanager

from config.database import get_database_config, get_pool_config


class AsyncMySQLManager:
    """异步MySQL数据库管理器"""
    
    def __init__(self):
        self.pool: Optional[aiomysql.Pool] = None
        self._config = get_database_config()
        self._pool_config = get_pool_config()
        self._initialized = False
    
    async def initialize(self) -> bool:
        """
        初始化数据库连接池
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            if self.pool:
                logger.warning("数据库连接池已存在，跳过初始化")
                return True
            
            logger.info("正在初始化MySQL连接池...")
            
            # 创建连接池
            self.pool = await aiomysql.create_pool(
                host=self._config['host'],
                port=self._config['port'],
                user=self._config['user'],
                password=self._config['password'],
                db=self._config['db'],
                charset=self._config['charset'],
                autocommit=self._config['autocommit'],
                connect_timeout=self._config['connect_timeout'],
                minsize=self._pool_config['minsize'],
                maxsize=self._pool_config['maxsize'],
                echo=self._pool_config['echo']
            )
            
            # 测试连接
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    result = await cursor.fetchone()
                    if result[0] != 1:
                        raise Exception("数据库连接测试失败")
            
            self._initialized = True
            logger.success(f"MySQL连接池初始化成功 - {self._config['host']}:{self._config['port']}/{self._config['db']}")
            return True
            
        except Exception as e:
            logger.error(f"MySQL连接池初始化失败: {e}")
            return False
    
    async def close(self):
        """关闭数据库连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            self.pool = None
            self._initialized = False
            logger.info("MySQL连接池已关闭")
    
    @asynccontextmanager
    async def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        Yields:
            aiomysql.Connection: 数据库连接
        """
        if not self._initialized or not self.pool:
            raise RuntimeError("数据库连接池未初始化，请先调用 initialize()")
        
        conn = None
        try:
            conn = await self.pool.acquire()
            yield conn
        finally:
            if conn:
                self.pool.release(conn)
    
    @asynccontextmanager
    async def get_cursor(self, connection=None):
        """
        获取数据库游标的上下文管理器
        
        Args:
            connection: 可选的数据库连接，如果不提供则自动获取
            
        Yields:
            aiomysql.Cursor: 数据库游标
        """
        if connection:
            # 使用提供的连接
            cursor = await connection.cursor(aiomysql.DictCursor)
            try:
                yield cursor
            finally:
                await cursor.close()
        else:
            # 自动获取连接
            async with self.get_connection() as conn:
                cursor = await conn.cursor(aiomysql.DictCursor)
                try:
                    yield cursor
                finally:
                    await cursor.close()
    
    async def execute_query(self, sql: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询SQL
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        try:
            async with self.get_cursor() as cursor:
                await cursor.execute(sql, params)
                results = await cursor.fetchall()
                return results if results else []
        except Exception as e:
            logger.error(f"查询执行失败: {sql}, 参数: {params}, 错误: {e}")
            raise
    
    async def execute_insert(self, sql: str, params: Optional[tuple] = None) -> int:
        """
        执行插入SQL
        
        Args:
            sql: SQL插入语句
            params: 插入参数
            
        Returns:
            int: 插入记录的ID
        """
        try:
            async with self.get_connection() as conn:
                async with self.get_cursor(conn) as cursor:
                    await cursor.execute(sql, params)
                    await conn.commit()
                    return cursor.lastrowid
        except Exception as e:
            logger.error(f"插入执行失败: {sql}, 参数: {params}, 错误: {e}")
            raise
    
    async def execute_update(self, sql: str, params: Optional[tuple] = None) -> int:
        """
        执行更新SQL
        
        Args:
            sql: SQL更新语句
            params: 更新参数
            
        Returns:
            int: 受影响的行数
        """
        try:
            async with self.get_connection() as conn:
                async with self.get_cursor(conn) as cursor:
                    await cursor.execute(sql, params)
                    await conn.commit()
                    return cursor.rowcount
        except Exception as e:
            logger.error(f"更新执行失败: {sql}, 参数: {params}, 错误: {e}")
            raise
    
    async def execute_delete(self, sql: str, params: Optional[tuple] = None) -> int:
        """
        执行删除SQL
        
        Args:
            sql: SQL删除语句
            params: 删除参数
            
        Returns:
            int: 受影响的行数
        """
        try:
            async with self.get_connection() as conn:
                async with self.get_cursor(conn) as cursor:
                    await cursor.execute(sql, params)
                    await conn.commit()
                    return cursor.rowcount
        except Exception as e:
            logger.error(f"删除执行失败: {sql}, 参数: {params}, 错误: {e}")
            raise
    
    async def execute_batch(self, sql: str, params_list: List[tuple]) -> int:
        """
        批量执行SQL
        
        Args:
            sql: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 受影响的总行数
        """
        try:
            async with self.get_connection() as conn:
                async with self.get_cursor(conn) as cursor:
                    await cursor.executemany(sql, params_list)
                    await conn.commit()
                    return cursor.rowcount
        except Exception as e:
            logger.error(f"批量执行失败: {sql}, 参数数量: {len(params_list)}, 错误: {e}")
            raise
    
    @asynccontextmanager
    async def transaction(self):
        """
        事务上下文管理器
        
        Yields:
            aiomysql.Connection: 事务连接
        """
        async with self.get_connection() as conn:
            try:
                await conn.begin()
                yield conn
                await conn.commit()
            except Exception as e:
                await conn.rollback()
                logger.error(f"事务回滚: {e}")
                raise
    
    async def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            List[Dict[str, Any]]: 表结构信息
        """
        sql = f"DESCRIBE {table_name}"
        return await self.execute_query(sql)
    
    async def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 表是否存在
        """
        sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = %s AND table_name = %s
        """
        result = await self.execute_query(sql, (self._config['db'], table_name))
        return result[0]['count'] > 0 if result else False


# 全局数据库管理器实例
_db_manager: Optional[AsyncMySQLManager] = None


async def get_db_manager() -> AsyncMySQLManager:
    """
    获取全局数据库管理器实例
    
    Returns:
        AsyncMySQLManager: 数据库管理器实例
    """
    global _db_manager
    if _db_manager is None:
        _db_manager = AsyncMySQLManager()
        await _db_manager.initialize()
    return _db_manager


async def close_db_manager():
    """关闭全局数据库管理器"""
    global _db_manager
    if _db_manager:
        await _db_manager.close()
        _db_manager = None


if __name__ == "__main__":
    # 测试数据库管理器
    async def test_db_manager():
        manager = AsyncMySQLManager()
        success = await manager.initialize()
        if success:
            logger.info("数据库管理器测试成功")
            # 测试查询
            result = await manager.execute_query("SELECT NOW() as current_time")
            logger.info(f"当前时间: {result}")
        await manager.close()
    
    asyncio.run(test_db_manager())
