"""
应用初始化器 - 纯粹的初始化和清理职责

提供统一的应用初始化和资源清理功能
初始化完成后，直接使用各个单例类，保持单例模式的纯粹性

Author: AI Assistant  
Date: 2025-07-29
"""

import asyncio
from typing import Dict, Any, Optional
from browser_manager import SingletonBrowserManager
from goofish_monitor import GoofishCookieMonitor
from scheduler import SimpleScheduler


class AppManager:
    """
    应用初始化器
    
    职责：
    1. 统一初始化应用组件
    2. 统一清理应用资源
    3. 提供初始化状态查询
    
    原则：
    - 只负责初始化，不提供组件获取方法
    - 初始化完成后，用户直接使用各个单例类
    - 保持单例模式的纯粹性
    """
    
    _instance = None
    _is_initialized = False
    _config = {}
    _scheduler = None
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    async def initialize(self, config: Dict[str, Any] = None):
        """
        统一初始化应用
        
        Args:
            config: 初始化配置
                - headless: bool, 是否无头模式，默认False
                - init_goofish_monitor: bool, 是否预初始化闲鱼监控器，默认False
                - auto_start_monitor: bool, 是否自动启动监控，默认True
                - chrome_path: str, 自定义Chrome路径，可选
                
        Raises:
            Exception: 初始化失败时抛出异常
        """
        if self._is_initialized:
            print("⚠️  应用已经初始化过了")
            return
            
        config = config or {}
        self._config = config.copy()
        
        print("🚀 正在初始化应用...")
        print(f"   配置: {config}")
        
        try:
            # 1. 初始化浏览器管理器
            print("📋 步骤1: 初始化浏览器管理器")
            browser_manager = SingletonBrowserManager()
            headless = config.get('headless', False)
            await browser_manager.get_browser(headless)
            print("✅ 浏览器管理器初始化完成")
            
            # 2. 可选：预初始化闲鱼监控器
            if config.get('init_goofish_monitor', False):
                print("📋 步骤2: 预初始化闲鱼监控器")
                monitor = GoofishCookieMonitor()
                auto_start = config.get('auto_start_monitor', True)
                await monitor.initialize(headless, auto_start)
                print("✅ 闲鱼监控器预初始化完成")

            # 3. 可选：初始化定时任务调度器
            if config.get('enable_scheduler', False):
                print("📋 步骤3: 初始化定时任务调度器")
                scheduler_config = config.get('scheduler_config', 'scheduler_config.json')
                self._scheduler = SimpleScheduler(scheduler_config)

                # 如果配置了自动启动，则启动调度器
                if config.get('auto_start_scheduler', True):
                    self._scheduler.start()
                    print("✅ 定时任务调度器已启动")
                else:
                    print("✅ 定时任务调度器已初始化（未启动）")

            self._is_initialized = True
            print("✅ 应用初始化完成")
            print("💡 现在可以直接使用 SingletonBrowserManager()、GoofishCookieMonitor() 和调度器")
            if self._scheduler:
                print(f"💡 调度器状态: {'运行中' if self._scheduler.running else '已停止'}")
            
        except Exception as e:
            print(f"❌ 应用初始化失败: {str(e)}")
            self._is_initialized = False
            raise
    
    async def cleanup(self):
        """
        统一清理应用资源
        
        清理所有已初始化的组件资源，包括：
        - 关闭浏览器实例
        - 停止监控任务
        - 清理其他全局资源
        """
        if not self._is_initialized:
            print("⚠️  应用未初始化，无需清理")
            return
            
        print("🧹 正在清理应用资源...")
        
        try:
            # 1. 停止定时任务调度器（如果存在）
            if self._scheduler:
                print("📋 步骤1: 停止定时任务调度器")
                self._scheduler.stop()
                self._scheduler = None
                print("✅ 定时任务调度器已停止")

            # 2. 清理闲鱼监控器（如果存在）
            print("📋 步骤2: 清理闲鱼监控器")
            # 注意：这里不直接创建新实例，而是检查是否有活跃的监控器
            # 通过GoofishCookieMonitor的类方法或全局状态来处理
            # 由于当前设计中没有全局状态跟踪，这里采用安全的方式
            try:
                # 创建临时实例来执行清理（如果有活跃监控器会被清理）
                temp_monitor = GoofishCookieMonitor()
                if temp_monitor.is_active():
                    await temp_monitor.close()
                    print("✅ 闲鱼监控器已清理")
            except Exception as e:
                print(f"⚠️  清理闲鱼监控器时出错: {str(e)}")
            
            # 3. 清理浏览器管理器
            print("📋 步骤3: 清理浏览器管理器")
            browser_manager = SingletonBrowserManager()
            if browser_manager.is_browser_active():
                await browser_manager.close_browser()
                print("✅ 浏览器管理器已清理")
            
            # 4. 重置状态
            self._is_initialized = False
            self._config.clear()
            self._scheduler = None  # 重置调度器引用
            AppManager._instance = None  # 重置单例实例
            
            print("✅ 应用资源清理完成")
            
        except Exception as e:
            print(f"❌ 清理应用资源时出错: {str(e)}")
            # 即使清理出错，也要重置状态
            self._is_initialized = False
            self._config.clear()
            raise
    
    @property
    def is_initialized(self) -> bool:
        """
        检查应用是否已初始化
        
        Returns:
            bool: True表示应用已初始化
        """
        return self._is_initialized
    
    def get_config(self, key: str = None) -> Any:
        """
        获取初始化配置
        
        Args:
            key: 配置键名，如果不提供则返回所有配置
            
        Returns:
            配置值或配置字典
        """
        if key:
            return self._config.get(key)
        return self._config.copy()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取应用状态信息
        
        Returns:
            包含应用状态的字典
        """
        status = {
            'is_initialized': self._is_initialized,
            'config': self._config.copy()
        }
        
        # 添加组件状态
        if self._is_initialized:
            try:
                browser_manager = SingletonBrowserManager()
                status['browser_active'] = browser_manager.is_browser_active()

                # 检查监控器状态（安全方式）
                temp_monitor = GoofishCookieMonitor()
                status['monitor_active'] = temp_monitor.is_active()
                if temp_monitor.is_active():
                    status['monitor_status'] = temp_monitor.get_monitor_status()

                # 检查调度器状态
                if self._scheduler:
                    status['scheduler_active'] = self._scheduler.running
                    status['scheduler_tasks'] = len(self._scheduler.get_all_tasks())
                else:
                    status['scheduler_active'] = False
                    status['scheduler_tasks'] = 0

            except Exception as e:
                status['status_check_error'] = str(e)
        
        return status

    @property
    def scheduler(self) -> Optional[SimpleScheduler]:
        """
        获取定时任务调度器实例

        Returns:
            Optional[SimpleScheduler]: 调度器实例，如果未启用则返回None
        """
        return self._scheduler


# 全局便捷函数
async def initialize_app(config: Dict[str, Any] = None) -> AppManager:
    """
    初始化应用的便捷函数
    
    Args:
        config: 初始化配置字典
        
    Returns:
        AppManager实例
        
    Example:
        await initialize_app({
            'headless': False,
            'init_goofish_monitor': True,
            'auto_start_monitor': True
        })
    """
    app = AppManager()
    await app.initialize(config)
    return app


async def cleanup_app():
    """
    清理应用的便捷函数
    
    Example:
        await cleanup_app()
    """
    app = AppManager()
    await app.cleanup()


def get_app_status() -> Dict[str, Any]:
    """
    获取应用状态的便捷函数
    
    Returns:
        应用状态字典
        
    Example:
        status = get_app_status()
        print(f"应用已初始化: {status['is_initialized']}")
    """
    app = AppManager()
    return app.get_status()


def is_app_initialized() -> bool:
    """
    检查应用是否已初始化的便捷函数
    
    Returns:
        bool: True表示应用已初始化
        
    Example:
        if is_app_initialized():
            # 可以直接使用组件
            browser_manager = SingletonBrowserManager()
    """
    app = AppManager()
    return app.is_initialized
