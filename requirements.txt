# XianYuApis 项目依赖文件
# 闲鱼第三方API集成库所需的Python包

# === 核心依赖 ===
# HTTP请求库
requests>=2.28.0

# 日志系统
loguru>=0.6.0

# WebSocket通信 (XianyuAutoAsync.py必需)
websockets>=10.4

# JavaScript执行引擎 (utils/xianyu_utils.py必需)
PyExecJS>=1.5.1

# Protobuf数据处理 (utils/xianyu_utils.py必需)
blackboxprotobuf>=1.0.1

# === 浏览器自动化依赖 ===
# Pyppeteer浏览器控制
pyppeteer>=1.0.2

# Pyppeteer反检测插件
pyppeteer-stealth>=2.7.4

# === 定时任务依赖 ===
# APScheduler定时任务框架 (scheduler.py使用)
APScheduler>=3.9.1

# === 开发和测试依赖 ===
# 代码格式化
black>=22.0.0

# 代码检查
flake8>=5.0.0

# 类型检查
mypy>=0.991

# 测试框架
pytest>=7.0.0
pytest-asyncio>=0.21.0

# === 可选依赖 ===
# 更好的JSON处理
orjson>=3.8.0

# 异步HTTP客户端 (可选，用于性能优化)
aiohttp>=3.8.0

# 环境变量管理
python-dotenv>=0.19.0

# === 数据库依赖 ===
# 异步MySQL驱动
aiomysql>=0.1.1

# 数据库连接池
aiopool