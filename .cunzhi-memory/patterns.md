# 常用模式和最佳实践

- 在1.py中实现了跨平台Chrome路径查找和Pyppeteer集成功能：包含find_chrome_path()跨平台查找函数、launch_pyppeteer_with_chrome()启动函数、demo_pyppeteer_usage()演示函数。支持Windows/macOS/Linux系统，可自动检测Chrome安装路径并用于Pyppeteer自动化。
- 在1.py中增强了Pyppeteer反检测功能：包含get_stealth_args()获取反检测启动参数、apply_stealth_settings()应用页面级反检测设置、支持pyppeteer_stealth插件集成。可有效隐藏webdriver属性、模拟Chrome对象、修改navigator属性等，降低被网站检测为机器人的风险。
- 1.py已重构为使用pyppeteer-stealth专业反检测方案：移除了手动反检测代码，简化为直接使用pyppeteer_stealth.stealth()函数。现在代码更简洁、更可靠，依赖专业的反检测库来隐藏自动化痕迹。需要安装pyppeteer-stealth依赖。
- 1.py增加了请求监听功能：专门监听https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search.shade/1.0这个闲鱼搜索API，能够捕获并打印完整的请求头、Cookie信息和POST数据。程序会访问闲鱼网站并尝试触发搜索来产生目标API请求。
- 1.py增加了_m_h5_tk_enc Cookie有效期监控功能：包含check_m_h5_tk_enc_validity()检查有效期、refresh_goofish_page()刷新页面更新Cookie、ensure_valid_m_h5_tk_enc()确保Cookie有效、start_cookie_monitor()后台持续监控。支持实时检测Cookie过期并自动刷新页面，适用于长时间运行的爬虫任务。
- 1.py重构为单例模式架构：新增SingletonBrowserManager单例浏览器管理器确保全局唯一浏览器实例，GoofishCookieMonitor闲鱼Cookie监控管理器封装监控功能并暴露page对象。提供便捷函数get_singleton_browser()和create_goofish_monitor()简化使用。支持资源管理、后台监控、页面共享等功能。
- 项目新增独立模块：browser_manager.py提供SingletonBrowserManager单例浏览器管理器，支持跨平台Chrome查找、反检测配置、资源管理；goofish_monitor.py提供GoofishCookieMonitor闲鱼Cookie监控器，支持自动访问闲鱼、后台监控Cookie有效期、页面对象暴露；example_usage.py提供完整使用示例；modules_README.md提供详细文档。模块化设计，便于项目集成使用。
