# 开发规范和规则

- 项目重构方案已确定：1)browser_manager.py和goofish_monitor.py保持根目录位置；2)保留核心便捷函数(get_singleton_browser, create_stealth_page, create_goofish_monitor)，移除冗余函数；3)新增app_manager.py作为纯粹的初始化器，只负责初始化和清理，不提供获取方法；4)初始化后直接使用各个单例类，符合单例模式原则
- 已修复requirements.txt依赖问题：补充了websockets、PyExecJS、blackboxprotobuf等关键缺失依赖，添加了APScheduler定时任务支持，包含开发测试工具(black、flake8、pytest)和可选性能优化包(aiohttp、orjson)。同时创建了完整的项目使用指南文档，涵盖环境准备、快速开始、高级配置和最佳实践。
- 已完成定时任务与应用初始化的轻量级集成：1)扩展AppManager类，添加SimpleScheduler管理功能；2)创建main.py统一入口，支持配置文件和自动任务注册；3)创建config_example.py配置模板，支持任务、日志、API等全面配置；4)新增集成使用说明文档，提供完整的使用指南。现在用户可以通过一个命令启动完整的应用，包括浏览器管理、定时任务调度和闲鱼API功能。
- 已将main.py简化为超简单版本：1)删除了复杂的配置文件系统(config_example.py)；2)采用硬编码配置方式，所有设置直接在main.py顶部修改；3)简化了主函数逻辑，从270行减少到122行；4)保留了核心集成功能：统一的应用管理、定时任务调度、资源清理；5)更新了使用说明文档，强调简单直接的使用方式。现在用户只需要在main.py中填入Cookie就可以一键启动完整应用。
- 为定时任务调度器添加了立即执行功能：1)在SimpleScheduler.add_task()方法中新增execute_immediately参数；2)在main.py中添加TEST_MODE配置开关；3)当execute_immediately=True时，任务添加后会立即执行一次用于测试；4)不影响原有的cron调度逻辑，只是额外执行一次；5)方便开发者测试任务功能，无需等待cron时间到达。
- 已实现任务模块化架构：1)创建tasks/文件夹，包含__init__.py、base_task.py、token_check_task.py、hourly_task.py；2)XianyuBaseTask基类提供通用功能(Cookie解析、令牌获取、用户信息等)；3)register_all_tasks()函数自动注册所有任务；4)main.py简化为只需调用register_all_tasks()；5)创建详细的任务添加指南文档，包含三种添加方法、Cron表达式详解、最佳实践和常见问题。现在添加新任务只需在tasks/文件夹创建新文件并在__init__.py中注册即可。
- 已删除所有复杂的定时任务，创建了最简单的测试任务：1)删除了base_task.py、token_check_task.py、hourly_task.py；2)创建了test_task.py，只做一件事print("测试任务")；3)任务每分钟执行一次(*/1 * * * *)；4)不需要Cookie，不依赖闲鱼API；5)main.py保持简洁，只有93行代码；6)TEST_MODE=True时会立即执行任务进行测试。现在是最简单的定时任务示例，便于理解和测试。
- 已去除tasks文件夹中对cookies_str的依赖：1)修改get_all_tasks()和get_default_tasks()函数，移除cookies_str参数；2)修改register_all_tasks()函数，移除cookies_str参数；3)在main.py中注释掉XIANYU_COOKIES变量；4)任务现在完全独立，如果需要Cookie等外部数据，需要在任务内部自行获取；5)这样设计更加灵活，每个任务可以根据自己的需求获取所需的数据，而不是统一传入。
- 已实现纯异步任务架构：1)修改BaseTask.execute()为async方法；2)在SimpleScheduler中添加_run_async_task()方法，使用新事件循环执行异步任务；3)修改_execute_task()调用_run_async_task()；4)更新test_task.py和xianyu_users_task.py为异步执行；5)现在所有任务都是异步的，可以直接使用await调用异步方法，解决了事件循环冲突问题。异步架构向下兼容同步操作，提供了最大的灵活性。
- 修复了事件循环冲突问题：使用线程池隔离方案，在_run_async_task()方法中使用concurrent.futures.ThreadPoolExecutor在独立线程中创建新事件循环执行异步任务。添加了5分钟超时保护，完全避免了与主事件循环的冲突。现在异步任务可以在调度器的独立线程中正常执行，不会出现"Cannot run the event loop while another loop is running"错误。
- 为GoofishCookieMonitor添加了跨事件循环支持：1)新增get_target_cookies_safe()方法，自动检测和处理跨循环问题；2)添加_get_cookies_cross_loop()方法使用run_coroutine_threadsafe在主循环中执行；3)添加_get_cookies_direct()降级方案直接访问页面对象；4)修改xianyu_users_task.py使用新的线程安全方法。现在单例监控器可以在任何事件循环中安全调用，保持了单例模式的完整性。
- 完成调度器异步化重构：1)将SimpleScheduler从线程模式改为异步模式；2)start()方法使用asyncio.create_task()启动；3)_run_scheduler_async()在主事件循环中运行；4)_execute_task_async()直接await任务执行；5)删除了线程相关代码和_run_async_task()方法；6)现在所有组件都在同一事件循环中运行，彻底解决了跨循环问题。任务可以直接使用await调用任何异步方法，无需特殊处理。
- 修复了立即执行的事件循环问题：1)修改add_task()方法中的execute_immediately逻辑，使用asyncio.create_task(self._execute_task_async())替代同步调用；2)删除了旧的_execute_task()方法，因为现在统一使用异步版本；3)现在立即执行和正常调度都使用相同的异步执行路径，确保所有任务都在主事件循环中运行。
