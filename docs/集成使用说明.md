# 🚀 XianYuApis 集成使用说明 - 简化版

## 📋 概述

本文档介绍如何使用集成了定时任务调度器的XianYuApis项目。现在采用**超简单**的硬编码配置方式，无需复杂的配置文件。

## 🎯 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 配置Cookie

直接编辑 `main.py` 文件，在配置区域填入您的闲鱼Cookie：

```python
# ================================
# 配置区域 - 请在这里修改您的设置
# ================================

# 闲鱼Cookie (必须填写)
XIANYU_COOKIES = "your_cookie_here"  # 填入您的闲鱼Cookie
```

### 3. 启动应用

```bash
# 一键启动
python main.py
```

## 🔧 核心功能

### 1. 统一应用管理

应用管理器 (`AppManager`) 现在集成了：
- ✅ 浏览器管理 (`SingletonBrowserManager`)
- ✅ 闲鱼监控 (`GoofishCookieMonitor`)  
- ✅ 定时任务调度 (`SimpleScheduler`)

### 2. 自动资源管理

```python
# 一键初始化所有组件
app = await initialize_app({
    'headless': False,
    'enable_scheduler': True,
    'auto_start_scheduler': True
})

# 自动清理所有资源
await cleanup_app()
```

### 3. 便捷的任务管理

```python
# 获取调度器实例
scheduler = app.scheduler

# 添加自定义任务
task = XianyuTask("my_task", cookies_str, "我的任务")
scheduler.add_task(task, "*/5 * * * *")  # 每5分钟执行
```

## 📝 配置选项详解

### 应用配置

在 `main.py` 中，您可以直接修改以下配置：

```python
# 应用配置
APP_CONFIG = {
    'headless': False,                    # 是否使用无头浏览器
    'enable_scheduler': True,             # 是否启用定时任务
    'auto_start_scheduler': True,         # 是否自动启动调度器
    'init_goofish_monitor': False,        # 是否初始化闲鱼监控器
    'auto_start_monitor': True,           # 是否自动启动监控
}
```

### 任务配置

您可以直接在 `main.py` 中修改或添加任务：

```python
# 添加示例任务 - 您可以在这里修改或添加更多任务
task1 = XianyuTask("token_check", XIANYU_COOKIES, "定期检查访问令牌")
scheduler.add_task(task1, "*/10 * * * *")  # 每10分钟执行

task2 = XianyuTask("hourly_task", XIANYU_COOKIES, "每小时任务")
scheduler.add_task(task2, "0 * * * *")  # 每小时执行

# 添加自定义任务
task3 = XianyuTask("daily_task", XIANYU_COOKIES, "每日任务")
scheduler.add_task(task3, "0 9 * * *")  # 每天9点执行
```

## 🎨 自定义任务开发

### 1. 创建任务类

```python
from scheduler import BaseTask
from XianyuApis import XianyuApis
from utils.xianyu_utils import trans_cookies, generate_device_id

class MyCustomTask(BaseTask):
    def __init__(self, task_id: str, cookies_str: str):
        super().__init__(task_id, "我的自定义任务")
        self.cookies_str = cookies_str
        self.api = XianyuApis()
    
    def execute(self):
        """任务执行逻辑"""
        cookies = trans_cookies(self.cookies_str)
        device_id = generate_device_id(cookies['unb'])
        
        # 执行您的业务逻辑
        result = self.api.get_token(cookies, device_id)
        return result
    
    def on_success(self, result):
        """成功回调"""
        print(f"任务执行成功: {result}")
    
    def on_error(self, error):
        """失败回调"""
        print(f"任务执行失败: {error}")
```

### 2. 注册任务

```python
# 方式1: 程序化注册
task = MyCustomTask("custom_task", cookies_str)
scheduler.add_task(task, "0 */2 * * *")  # 每2小时执行

# 方式2: 配置文件注册
SCHEDULED_TASKS = [
    {
        'task_id': 'custom_task',
        'name': '自定义任务',
        'cron': '0 */2 * * *',
        'enabled': True
    }
]
```

## 🔍 监控和调试

### 1. 查看应用状态

```python
from app_manager import get_app_status

status = get_app_status()
print(f"调度器运行: {status['scheduler_active']}")
print(f"任务数量: {status['scheduler_tasks']}")
```

### 2. 查看任务状态

```python
# 获取所有任务信息
tasks = scheduler.get_all_tasks()
for task in tasks:
    print(f"任务: {task['name']}, 状态: {task.get('last_result', {}).get('status', 'unknown')}")

# 获取特定任务状态
task_result = scheduler.get_task_status("token_check")
if task_result:
    print(f"任务状态: {task_result.status}")
```

### 3. 日志查看

```bash
# 查看实时日志
tail -f logs/xianyu_app_*.log

# 查看错误日志
grep "ERROR" logs/xianyu_app_*.log
```

## 🛠️ 常用操作

### 1. 启动和停止

```python
# 启动应用
python main.py

# 优雅停止 (Ctrl+C)
# 或发送SIGTERM信号
kill -TERM <pid>
```

### 2. 任务管理

```python
# 添加任务
scheduler.add_task(task, "*/5 * * * *")

# 移除任务
scheduler.remove_task("task_id")

# 启动调度器
scheduler.start()

# 停止调度器
scheduler.stop()
```

### 3. 配置更新

```python
# 重新加载配置
import importlib
import config
importlib.reload(config)

# 应用新配置
await app.initialize(config.APP_CONFIG)
```

## ⚠️ 注意事项

### 1. Cookie管理
- Cookie必须来自已登录的闲鱼账号
- 定期检查Cookie有效性
- 避免在代码中硬编码Cookie

### 2. 任务频率
- 避免过于频繁的任务执行
- 合理设置任务间隔，防止触发反爬虫
- 监控任务执行时间，避免重叠执行

### 3. 资源管理
- 应用退出时会自动清理资源
- 长时间运行建议定期重启
- 监控内存和CPU使用情况

### 4. 错误处理
- 任务失败会自动记录日志
- 实现适当的重试机制
- 设置告警通知（可选）

## 🔧 故障排除

### 常见问题

1. **Cookie失效**
   ```
   解决方案: 重新获取Cookie并更新配置文件
   ```

2. **任务不执行**
   ```
   检查: cron表达式是否正确
   检查: 任务是否启用 (enabled: true)
   检查: 调度器是否运行
   ```

3. **浏览器启动失败**
   ```
   检查: Chrome是否正确安装
   检查: 系统权限是否足够
   尝试: 设置 headless: true
   ```

4. **内存占用过高**
   ```
   解决方案: 定期重启应用
   优化: 减少并发任务数量
   配置: 启用headless模式
   ```

## 📚 更多资源

- [项目分析报告](./项目分析报告.md) - 详细技术架构
- [项目使用指南](./项目使用指南.md) - 基础使用方法
- [定时任务使用说明](./定时任务使用说明.md) - 调度器详细说明

---

**祝您使用愉快！** 🎉
