# 🚀 XianYuApis 项目使用指南

## 📋 项目简介

XianYuApis 是一个功能完整的闲鱼第三方API集成库，提供HTTP接口调用、WebSocket实时通信、浏览器自动化和定时任务等功能。

## 🛠️ 环境准备

### 系统要求
- **Python**: 3.7+ (推荐 3.9+)
- **Node.js**: 18+ (用于JavaScript加密算法)
- **操作系统**: Windows/macOS/Linux

### 依赖安装

#### 1. 安装Python依赖
```bash
# 安装所有依赖
pip install -r requirements.txt

# 或者只安装核心依赖
pip install requests loguru websockets PyExecJS blackboxprotobuf pyppeteer pyppeteer-stealth
```

#### 2. 安装Node.js依赖 (如果需要)
```bash
npm install
```

#### 3. 验证安装
```bash
python -c "import requests, loguru, websockets, execjs, blackboxprotobuf; print('✅ 核心依赖安装成功')"
```

## 🎯 快速开始

### 1. 获取闲鱼Cookie

**重要**: 必须使用已登录闲鱼账号的Cookie，未登录的Cookie无效。

1. 打开浏览器，访问 https://www.goofish.com
2. 登录您的闲鱼账号
3. 按F12打开开发者工具
4. 在Network标签页中找到任意请求
5. 复制Cookie字符串

### 2. 基础HTTP API使用

```python
from XianyuApis import XianyuApis
from utils.xianyu_utils import trans_cookies, generate_device_id

# 配置Cookie
cookies_str = "your_cookie_string_here"
cookies = trans_cookies(cookies_str)

# 创建API实例
api = XianyuApis()

# 生成设备ID
device_id = generate_device_id(cookies.get('unb', 'default'))

# 获取访问令牌
try:
    result = api.get_token(cookies, device_id)
    print(f"✅ 获取令牌成功: {result}")
except Exception as e:
    print(f"❌ 获取令牌失败: {e}")
```

### 3. WebSocket实时通信使用

```python
import asyncio
from XianyuAutoAsync import XianyuLive

# 配置Cookie
cookies_str = "your_cookie_string_here"

# 创建WebSocket客户端
client = XianyuLive(cookies_str)

# 发送单条消息
async def send_message():
    to_id = "目标用户ID"
    item_id = "商品ID"
    message = "Hello, World!"
    
    await client.send_msg_once(to_id, item_id, message)
    print("✅ 消息发送成功")

# 启动消息监听
async def start_listening():
    await client.main()

# 运行示例
if __name__ == "__main__":
    # 发送消息
    # asyncio.run(send_message())
    
    # 或启动监听服务
    asyncio.run(start_listening())
```

### 4. 浏览器自动化使用

```python
import asyncio
from browser_manager import SingletonBrowserManager
from goofish_monitor import GoofishCookieMonitor

async def browser_example():
    # 获取浏览器实例
    browser_manager = SingletonBrowserManager()
    browser = await browser_manager.get_browser(headless=False)
    
    # 创建页面
    page = await browser.newPage()
    await page.goto('https://www.goofish.com')
    
    print("✅ 浏览器启动成功")
    
    # 使用完毕后清理
    await browser_manager.cleanup()

# 运行示例
asyncio.run(browser_example())
```

### 5. 定时任务使用

```python
from scheduler import SimpleScheduler, BaseTask
from XianyuApis import XianyuApis
from utils.xianyu_utils import trans_cookies, generate_device_id

class MyXianyuTask(BaseTask):
    def __init__(self, task_id: str, cookies_str: str):
        super().__init__(task_id, "闲鱼定时任务", "定期执行闲鱼API操作")
        self.cookies_str = cookies_str
        self.api = XianyuApis()
    
    def execute(self):
        """任务执行逻辑"""
        cookies = trans_cookies(self.cookies_str)
        device_id = generate_device_id(cookies.get('unb', 'default'))
        
        # 执行具体的API操作
        result = self.api.get_token(cookies, device_id)
        return f"任务执行成功: {result}"

# 创建调度器
scheduler = SimpleScheduler()

# 添加任务
task = MyXianyuTask("daily_task", "your_cookie_string")
scheduler.add_task(task, "0 9 * * *")  # 每天9点执行

# 启动调度器
scheduler.start()

# 保持程序运行
try:
    import time
    while True:
        time.sleep(60)
except KeyboardInterrupt:
    scheduler.stop()
    print("调度器已停止")
```

## 🔧 高级配置

### 1. 应用初始化管理

```python
import asyncio
from app_manager import initialize_app, cleanup_app

async def advanced_setup():
    # 初始化应用
    app = await initialize_app({
        'headless': False,                # 浏览器是否无头模式
        'init_goofish_monitor': True,     # 是否预初始化闲鱼监控
        'auto_start_monitor': True        # 是否自动启动监控
    })
    
    # 使用各个组件
    # ... 你的业务逻辑 ...
    
    # 清理资源
    await cleanup_app()

asyncio.run(advanced_setup())
```

### 2. 错误处理最佳实践

```python
import logging
from loguru import logger

# 配置日志
logger.add("logs/xianyu_{time}.log", rotation="1 day", retention="7 days")

try:
    # 你的代码
    pass
except Exception as e:
    logger.error(f"操作失败: {e}")
    # 处理错误逻辑
```

## ⚠️ 注意事项

### 安全提醒
1. **Cookie安全**: 不要在代码中硬编码Cookie，使用环境变量或配置文件
2. **频率控制**: 避免高频请求，防止触发反爬虫机制
3. **合规使用**: 仅用于学习和研究目的，遵守相关法律法规

### 常见问题
1. **Cookie失效**: 定期更新Cookie，监控登录状态
2. **网络错误**: 添加重试机制和异常处理
3. **依赖冲突**: 使用虚拟环境隔离项目依赖

## 📚 更多资源

- [项目分析报告](./项目分析报告.md) - 详细的技术架构说明
- [定时任务使用说明](./定时任务使用说明.md) - 定时任务详细配置
- [GitHub仓库](https://github.com/cv-cat/XianYuApis) - 获取最新版本

## 🤝 支持与反馈

如果您在使用过程中遇到问题，请：
1. 查看项目文档和示例代码
2. 检查依赖安装是否完整
3. 确认Cookie是否有效
4. 提交Issue或联系作者

---
**祝您使用愉快！** 🎉
