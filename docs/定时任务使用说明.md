# 🕐 XianYu定时任务框架使用说明

## 📋 概述

本框架为XianYuApis项目提供了类似cron的定时任务功能，支持定时执行各种任务，特别适合与XianyuApis.py集成使用。

## 🏗️ 架构设计

### 核心组件
- **SimpleScheduler**: 定时任务调度器
- **BaseTask**: 任务基类，用户需要继承实现
- **CronParser**: Cron表达式解析器
- **TaskResult**: 任务执行结果记录

### 文件结构
```
├── scheduler.py           # 核心调度器模块
├── example_tasks.py       # 示例任务实现
├── docs/
│   └── 定时任务使用说明.md # 本文档
└── scheduler_config.json  # 任务配置文件（自动生成）
```

## 🚀 快速开始

### 1. 基本使用

```python
from scheduler import SimpleScheduler, BaseTask

# 创建自定义任务
class MyTask(BaseTask):
    def __init__(self, task_id: str):
        super().__init__(task_id, "我的任务", "任务描述")
    
    def execute(self):
        # 实现具体的任务逻辑
        print("执行任务...")
        return {"status": "success"}

# 创建调度器
scheduler = SimpleScheduler()

# 添加任务
task = MyTask("my_task_1")
scheduler.add_task(task, "*/5 * * * *")  # 每5分钟执行

# 启动调度器
scheduler.start()
```

### 2. 与XianyuApis集成

```python
from XianyuApis import XianyuApis
from scheduler import BaseTask

class XianyuTask(BaseTask):
    def __init__(self, task_id: str, cookies_str: str):
        super().__init__(task_id, "闲鱼API任务")
        self.cookies_str = cookies_str
        self.xianyu_api = XianyuApis()
    
    def execute(self):
        # 使用XianyuApis执行具体操作
        cookies = trans_cookies(self.cookies_str)
        device_id = generate_device_id(cookies.get('unb', 'default'))
        result = self.xianyu_api.get_token(cookies, device_id)
        return result
```

## 📝 Cron表达式格式

支持标准的5位cron表达式格式：

```
分 时 日 月 周
*  *  *  *  *
```

### 字段说明
- **分**: 0-59
- **时**: 0-23  
- **日**: 1-31
- **月**: 1-12
- **周**: 0-7 (0和7都表示周日)

### 特殊字符
- `*`: 匹配任意值
- `*/n`: 每n个单位执行一次
- `n-m`: 范围匹配
- `n,m`: 列表匹配

### 常用示例
```python
"0 * * * *"      # 每小时的0分执行
"*/5 * * * *"    # 每5分钟执行
"0 9 * * *"      # 每天上午9点执行
"0 9 * * 1-5"    # 工作日上午9点执行
"0 2 1 * *"      # 每月1号凌晨2点执行
"0 0 * * 0"      # 每周日午夜执行
```

## 🛠️ API参考

### SimpleScheduler类

#### 初始化
```python
scheduler = SimpleScheduler(config_file="scheduler_config.json")
```

#### 主要方法

**add_task(task, cron_expr, enabled=True)**
- 添加定时任务
- `task`: BaseTask实例
- `cron_expr`: cron表达式
- `enabled`: 是否启用

**remove_task(task_id)**
- 移除任务
- 返回: bool (是否成功)

**start()**
- 启动调度器

**stop()**
- 停止调度器

**get_task_status(task_id)**
- 获取任务状态
- 返回: TaskResult对象

**get_all_tasks()**
- 获取所有任务信息
- 返回: List[Dict]

### BaseTask类

#### 必须实现的方法

**execute()**
- 任务执行逻辑
- 返回: Any (任务结果)

#### 可选重写的方法

**on_success(result)**
- 任务成功回调
- `result`: execute()的返回值

**on_error(error)**
- 任务失败回调
- `error`: Exception对象

## 📊 任务状态管理

### 任务状态
- `PENDING`: 等待执行
- `RUNNING`: 正在执行
- `COMPLETED`: 执行完成
- `FAILED`: 执行失败
- `STOPPED`: 已停止

### 查看任务状态
```python
# 获取单个任务状态
result = scheduler.get_task_status("task_id")
if result:
    print(f"状态: {result.status}")
    print(f"开始时间: {result.start_time}")
    print(f"结束时间: {result.end_time}")
    print(f"结果: {result.result}")

# 获取所有任务信息
tasks = scheduler.get_all_tasks()
for task in tasks:
    print(f"任务: {task['name']}")
    print(f"状态: {task.get('last_result', {}).get('status', 'N/A')}")
```

## 📁 配置文件

调度器会自动创建和维护配置文件 `scheduler_config.json`：

```json
{
  "schedules": {
    "task_id": {
      "name": "任务名称",
      "description": "任务描述",
      "cron_expr": "*/5 * * * *",
      "cron_config": {
        "minute": "*/5"
      },
      "enabled": true,
      "created_at": "2025-07-29T10:00:00",
      "last_run": "2025-07-29T10:05:00",
      "next_run": null
    }
  },
  "updated_at": "2025-07-29T10:05:00"
}
```

## 🔧 高级用法

### 1. 任务依赖管理

```python
class DependentTask(BaseTask):
    def __init__(self, task_id: str, dependency_task_id: str, scheduler: SimpleScheduler):
        super().__init__(task_id, "依赖任务")
        self.dependency_task_id = dependency_task_id
        self.scheduler = scheduler
    
    def execute(self):
        # 检查依赖任务状态
        dep_result = self.scheduler.get_task_status(self.dependency_task_id)
        if not dep_result or dep_result.status != TaskStatus.COMPLETED:
            raise Exception(f"依赖任务 {self.dependency_task_id} 未完成")
        
        # 执行当前任务
        return {"dependency_result": dep_result.result}
```

### 2. 动态任务管理

```python
# 运行时添加任务
def add_dynamic_task(scheduler, task_config):
    task = MyTask(task_config['id'])
    scheduler.add_task(task, task_config['cron'])

# 运行时禁用任务
def disable_task(scheduler, task_id):
    if task_id in scheduler.schedules:
        scheduler.schedules[task_id]['enabled'] = False
        scheduler.save_config()
```

### 3. 错误处理和重试

```python
class RetryTask(BaseTask):
    def __init__(self, task_id: str, max_retries: int = 3):
        super().__init__(task_id, "重试任务")
        self.max_retries = max_retries
        self.retry_count = 0
    
    def execute(self):
        for attempt in range(self.max_retries):
            try:
                # 执行任务逻辑
                result = self.do_work()
                return result
            except Exception as e:
                self.retry_count = attempt + 1
                if attempt == self.max_retries - 1:
                    raise e
                time.sleep(2 ** attempt)  # 指数退避
    
    def do_work(self):
        # 具体的工作逻辑
        pass
```

## ⚠️ 注意事项

1. **线程安全**: 调度器使用多线程，确保任务代码是线程安全的
2. **异常处理**: 任务中的异常会被捕获，不会影响调度器运行
3. **资源管理**: 长时间运行的任务可能影响其他任务，注意资源使用
4. **配置备份**: 定期备份配置文件，避免任务配置丢失
5. **日志监控**: 关注日志输出，及时发现和处理问题

## 🐛 故障排除

### 常见问题

**Q: 任务没有按时执行**
A: 检查cron表达式格式，确认任务已启用，查看日志错误信息

**Q: 调度器启动失败**
A: 检查配置文件格式，确认没有语法错误

**Q: 任务执行失败**
A: 查看TaskResult中的error信息，检查任务代码逻辑

**Q: 内存占用过高**
A: 检查是否有任务产生内存泄漏，考虑重启调度器

### 调试模式

```python
import logging
logging.getLogger('XianYuScheduler').setLevel(logging.DEBUG)
```

## 📈 性能优化

1. **任务执行时间**: 避免长时间运行的任务阻塞调度器
2. **内存使用**: 及时清理任务结果，避免内存积累
3. **并发控制**: 对于资源密集型任务，考虑添加并发限制
4. **配置优化**: 合理设置任务执行频率，避免过于频繁

---

**开发者**: 外包程序员  
**更新时间**: 2025-07-29  
**版本**: 1.0.0
