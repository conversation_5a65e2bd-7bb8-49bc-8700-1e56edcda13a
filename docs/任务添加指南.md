# 📋 XianYuApis 任务添加指南

## 🎯 概述

本指南详细介绍如何在XianYuApis项目中添加和管理定时任务。现在所有任务都在 `tasks/` 文件夹中独立管理，使项目结构更清晰、更易维护。

## 📁 任务模块结构

```
XianYuApis/
├── tasks/
│   ├── __init__.py              # 任务模块导出
│   ├── base_task.py             # 闲鱼任务基类
│   ├── token_check_task.py      # 令牌检查任务
│   ├── hourly_task.py           # 每小时任务示例
│   └── your_custom_task.py      # 您的自定义任务
├── main.py                      # 主程序
└── scheduler.py                 # 调度器
```

## 🔧 添加新任务的三种方法

### 方法1: 创建新的任务文件（推荐）

#### 步骤1: 创建任务文件

在 `tasks/` 文件夹中创建新文件，例如 `my_custom_task.py`：

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
我的自定义任务 - 任务描述

Author: 您的名字
Date: 2025-07-30
"""

from .base_task import XianyuBaseTask
from loguru import logger


class MyCustomTask(XianyuBaseTask):
    """我的自定义任务类"""
    
    def __init__(self, task_id: str, cookies_str: str):
        super().__init__(
            task_id=task_id,
            name="我的自定义任务",
            description="这里写任务的详细描述",
            cookies_str=cookies_str
        )
    
    def execute(self):
        """任务执行逻辑"""
        logger.info(f"🚀 开始执行自定义任务: {self.task_id}")
        
        try:
            # 1. 您的业务逻辑
            result = self._do_something()
            
            # 2. 返回结果
            return {
                'status': 'success',
                'message': '任务执行成功',
                'data': result
            }
            
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            raise
    
    def _do_something(self):
        """您的具体业务逻辑"""
        # 这里实现您的任务逻辑
        # 可以使用 self.get_token() 获取令牌
        # 可以使用 self.api 调用闲鱼API
        # 可以使用 self.cookies 访问Cookie信息
        
        return "执行结果"
```

#### 步骤2: 在 `__init__.py` 中注册任务

编辑 `tasks/__init__.py`，添加您的任务：

```python
# 导入您的任务
from .my_custom_task import MyCustomTask

# 添加到导出列表
__all__ = [
    'XianyuBaseTask',
    'TokenCheckTask', 
    'HourlyTask',
    'MyCustomTask',  # 新增
    # ...
]

# 在 get_all_tasks 函数中添加任务配置
def get_all_tasks(cookies_str: str):
    tasks = [
        (TokenCheckTask("token_check", cookies_str), "*/10 * * * *", True),
        (HourlyTask("hourly_task", cookies_str), "0 * * * *", True),
        (MyCustomTask("my_task", cookies_str), "0 */2 * * *", True),  # 每2小时执行
    ]
    return tasks
```

### 方法2: 直接在main.py中添加

如果是简单的一次性任务，可以直接在 `main.py` 中添加：

```python
# 在main.py中
from tasks import TokenCheckTask

# 在任务注册部分
def add_custom_tasks(scheduler, cookies_str, test_mode):
    # 添加自定义任务
    custom_task = TokenCheckTask("custom_check", cookies_str)
    scheduler.add_task(custom_task, "*/5 * * * *", execute_immediately=test_mode)
    
    logger.info("✅ 自定义任务已添加")

# 在main函数中调用
register_all_tasks(app.scheduler, XIANYU_COOKIES, TEST_MODE)
add_custom_tasks(app.scheduler, XIANYU_COOKIES, TEST_MODE)  # 添加这行
```

### 方法3: 动态任务配置

创建配置文件来管理任务：

```python
# 创建 task_config.py
TASK_CONFIGS = [
    {
        'class': 'TokenCheckTask',
        'task_id': 'token_check_1',
        'cron': '*/10 * * * *',
        'enabled': True
    },
    {
        'class': 'HourlyTask', 
        'task_id': 'hourly_1',
        'cron': '0 * * * *',
        'enabled': True
    }
]
```

## ⏰ Cron表达式详解

Cron表达式格式：`分 时 日 月 周`

### 常用表达式示例

```bash
# 基本时间间隔
"*/5 * * * *"     # 每5分钟执行
"*/10 * * * *"    # 每10分钟执行
"*/30 * * * *"    # 每30分钟执行

# 整点执行
"0 * * * *"       # 每小时整点执行
"0 */2 * * *"     # 每2小时执行
"0 */6 * * *"     # 每6小时执行

# 每日执行
"0 9 * * *"       # 每天上午9点执行
"0 18 * * *"      # 每天下午6点执行
"30 23 * * *"     # 每天晚上11:30执行

# 每周执行
"0 9 * * 1"       # 每周一上午9点执行
"0 0 * * 0"       # 每周日零点执行

# 每月执行
"0 0 1 * *"       # 每月1日零点执行
"0 9 15 * *"      # 每月15日上午9点执行
```

### 表达式字段说明

| 字段 | 允许值 | 特殊字符 | 说明 |
|------|--------|----------|------|
| 分   | 0-59   | * / ,    | 分钟 |
| 时   | 0-23   | * / ,    | 小时 |
| 日   | 1-31   | * / ,    | 日期 |
| 月   | 1-12   | * / ,    | 月份 |
| 周   | 0-7    | * / ,    | 星期（0和7都表示周日）|

### 特殊字符含义

- `*` : 匹配任意值
- `/` : 指定间隔，如 `*/5` 表示每5个单位
- `,` : 列举多个值，如 `1,3,5` 表示1、3、5

## 🛠️ 任务开发最佳实践

### 1. 继承基类

所有闲鱼相关任务都应该继承 `XianyuBaseTask`：

```python
from .base_task import XianyuBaseTask

class MyTask(XianyuBaseTask):
    def __init__(self, task_id: str, cookies_str: str):
        super().__init__(task_id, "任务名称", "任务描述", cookies_str)
```

### 2. 实现execute方法

```python
def execute(self):
    """任务执行逻辑"""
    try:
        # 您的业务逻辑
        result = self._do_work()
        
        # 返回结构化结果
        return {
            'status': 'success',
            'message': '执行成功',
            'data': result,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        raise
```

### 3. 使用基类提供的方法

```python
def execute(self):
    # 获取访问令牌
    token_result = self.get_token()
    
    # 验证Cookie有效性
    if not self.validate_cookies():
        raise Exception("Cookie已失效")
    
    # 获取用户信息
    user_info = self.get_user_info()
    
    # 您的业务逻辑...
```

### 4. 错误处理

```python
def execute(self):
    try:
        # 业务逻辑
        pass
    except Exception as e:
        # 记录详细错误信息
        logger.error(f"任务 {self.task_id} 执行失败: {e}")
        logger.error(f"用户ID: {self.user_id}")
        logger.error(f"设备ID: {self.device_id}")
        raise
```

## 🧪 任务测试

### 1. 立即执行测试

在 `main.py` 中设置：

```python
TEST_MODE = True  # 任务添加后立即执行
```

### 2. 单独测试任务

```python
# 创建测试脚本 test_task.py
from tasks.my_custom_task import MyCustomTask

cookies_str = "your_cookie_here"
task = MyCustomTask("test", cookies_str)

try:
    result = task.execute()
    print(f"测试成功: {result}")
except Exception as e:
    print(f"测试失败: {e}")
```

### 3. 调试模式

```python
# 在任务中添加调试信息
def execute(self):
    logger.debug(f"开始执行任务: {self.task_id}")
    logger.debug(f"用户ID: {self.user_id}")
    logger.debug(f"设备ID: {self.device_id}")
    
    # 您的逻辑...
```

## 📊 任务监控

### 1. 查看任务状态

```python
# 在main.py运行时会显示
status = get_app_status()
print(f"调度器运行: {status['scheduler_active']}")
print(f"任务数量: {status['scheduler_tasks']}")
```

### 2. 查看执行日志

```bash
# 查看日志文件
tail -f logs/xianyu_app_*.log

# 过滤特定任务
grep "token_check" logs/xianyu_app_*.log
```

### 3. 任务执行历史

调度器会自动保存任务执行历史到 `scheduler_config.json`。

## ❓ 常见问题

### Q: 如何修改现有任务的执行时间？

A: 编辑 `tasks/__init__.py` 中的 `get_all_tasks` 函数，修改对应任务的cron表达式。

### Q: 如何临时禁用某个任务？

A: 在 `get_all_tasks` 函数中将任务的 `enabled` 参数设为 `False`。

### Q: 如何添加任务执行前的条件检查？

A: 在任务的 `execute` 方法开始处添加条件判断：

```python
def execute(self):
    # 条件检查
    if not self._should_execute():
        logger.info("条件不满足，跳过执行")
        return {'status': 'skipped', 'reason': '条件不满足'}
    
    # 正常执行逻辑...
```

### Q: 如何在任务间共享数据？

A: 可以使用文件、数据库或全局变量：

```python
# 使用文件共享
import json

def save_shared_data(data):
    with open('shared_data.json', 'w') as f:
        json.dump(data, f)

def load_shared_data():
    try:
        with open('shared_data.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}
```

---

**祝您使用愉快！** 🎉
