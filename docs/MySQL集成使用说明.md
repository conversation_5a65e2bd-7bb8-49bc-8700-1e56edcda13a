# MySQL数据库集成使用说明

## 📋 概述

本项目已成功集成MySQL数据库，支持闲鱼用户数据的持久化存储。集成包含异步数据库连接池、数据模型、自动化任务存储等功能。

## 🗄️ 数据库配置

### 连接信息
- **主机**: ***********:3309
- **数据库**: de_48eidc
- **用户名**: de_cfTnAG
- **密码**: de_Bz6Sr4

### 配置文件
数据库配置位于 `config/database.py`，支持环境变量覆盖：

```python
# 环境变量覆盖示例
export MYSQL_HOST=***********
export MYSQL_PORT=3309
export MYSQL_USER=de_cfTnAG
export MYSQL_PASSWORD=de_Bz6Sr4
export MYSQL_DATABASE=de_48eidc
```

## 📊 数据表结构

### xianyu_users 表
存储闲鱼用户的完整信息，包括：

- **基础信息**: 用户ID、昵称、头像、归属地
- **用户标签**: 实名认证、用户升级、芝麻认证等
- **店铺信息**: 等级、积分、好评率、评价数
- **社交数据**: 关注状态、粉丝数、关注数
- **统计数据**: 商品数量、评价数量
- **信用体系**: 买家等级、信用文本、图标
- **系统字段**: API追踪ID、原始JSON数据、时间戳

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install aiomysql aiopool
```

### 2. 测试数据库连接

```bash
python test_database.py
```

### 3. 运行完整应用

```bash
python main.py
```

## 💻 代码使用示例

### 基础数据库操作

```python
from db.mysql_manager import get_db_manager

# 获取数据库管理器
db_manager = await get_db_manager()

# 执行查询
results = await db_manager.execute_query("SELECT * FROM xianyu_users LIMIT 10")

# 执行插入
user_id = await db_manager.execute_insert(
    "INSERT INTO xianyu_users (user_id, display_name) VALUES (%s, %s)",
    ("123456", "测试用户")
)
```

### 用户模型操作

```python
from models.xianyu_user import XianyuUser

# 从API响应创建用户
user = XianyuUser.from_api_response(api_data)

# 保存到数据库
user_id = await user.save()

# 查询用户
user = await XianyuUser.find_by_user_id("2201292691262")

# 统计用户数量
count = await XianyuUser.count_all()
```

### 事务处理

```python
from db.mysql_manager import get_db_manager

db_manager = await get_db_manager()

async with db_manager.transaction() as conn:
    # 在事务中执行多个操作
    cursor = await conn.cursor()
    await cursor.execute("INSERT INTO ...")
    await cursor.execute("UPDATE ...")
    # 自动提交或回滚
```

## 🔧 项目结构

```
XianYuApis/
├── config/
│   ├── __init__.py
│   └── database.py          # 数据库配置
├── db/
│   ├── __init__.py
│   └── mysql_manager.py     # 异步MySQL管理器
├── models/
│   ├── __init__.py
│   └── xianyu_user.py       # 用户数据模型
├── sql/
│   └── xianyu_users_table.sql  # 数据表结构
├── tasks/
│   └── xianyu_users_task.py    # 用户采集任务(已集成数据库)
├── test_database.py           # 数据库测试脚本
└── main.py                   # 主程序(已集成数据库初始化)
```

## 📈 功能特性

### 异步数据库连接池
- 支持连接池管理，提高并发性能
- 自动连接回收和错误处理
- 支持事务处理

### 数据模型ORM
- 自动解析API响应数据
- 支持数据验证和转换
- 提供CRUD操作方法

### 自动化任务集成
- 定时任务自动保存用户数据
- 支持批量数据处理
- 错误处理和重试机制

### 数据完整性
- 唯一键约束防止重复数据
- 自动更新时间戳
- 原始JSON数据备份

## 🛠️ 高级配置

### 连接池配置

```python
# config/database.py
POOL_CONFIG = {
    'minsize': 1,           # 最小连接数
    'maxsize': 10,          # 最大连接数
    'pool_recycle': 3600,   # 连接回收时间(秒)
    'echo': False,          # 是否打印SQL
}
```

### 日志配置

数据库操作会自动记录到日志中，包括：
- 连接状态
- SQL执行情况
- 错误信息
- 性能统计

## 🔍 监控和维护

### 数据库状态检查

```python
# 检查表是否存在
exists = await db_manager.table_exists('xianyu_users')

# 获取表结构信息
table_info = await db_manager.get_table_info('xianyu_users')

# 统计数据量
count = await XianyuUser.count_all()
```

### 性能优化建议

1. **索引优化**: 已为常用查询字段建立索引
2. **连接池调优**: 根据并发需求调整连接池大小
3. **批量操作**: 使用 `execute_batch` 进行批量插入
4. **数据清理**: 定期清理过期数据

## ❗ 注意事项

1. **数据库连接**: 确保数据库服务可访问
2. **表结构**: 首次使用需要执行 `sql/xianyu_users_table.sql` 创建表
3. **权限**: 确保数据库用户有足够的操作权限
4. **网络**: 注意网络延迟对性能的影响
5. **备份**: 定期备份重要数据

## 🐛 故障排除

### 常见问题

1. **连接失败**: 检查网络和数据库配置
2. **权限错误**: 确认数据库用户权限
3. **表不存在**: 执行建表SQL脚本
4. **编码问题**: 确保使用utf8mb4字符集

### 调试方法

```python
# 启用SQL日志
POOL_CONFIG['echo'] = True

# 查看详细错误信息
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 技术支持

如有问题，请检查：
1. 日志文件中的错误信息
2. 数据库连接状态
3. 网络连通性
4. 配置文件正确性
