-- 闲鱼用户信息表
CREATE TABLE `xianyu_users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 用户基础信息
  `user_id` varchar(50) NOT NULL COMMENT '用户ID(kcUserId)',
  `encrypted_user_id` varchar(255) DEFAULT NULL COMMENT '加密用户ID',
  `display_name` varchar(100) DEFAULT NULL COMMENT '显示昵称',
  `avatar_url` text DEFAULT NULL COMMENT '头像URL',
  `user_type` tinyint(4) DEFAULT 1 COMMENT '用户类型',
  `ip_location` varchar(50) DEFAULT NULL COMMENT 'IP归属地',
  `is_self` tinyint(1) DEFAULT 0 COMMENT '是否为本人',
  `ios_verify` tinyint(1) DEFAULT 0 COMMENT 'iOS验证状态',
  
  -- 用户标签信息
  `real_name_certified` tinyint(1) DEFAULT 0 COMMENT '实名认证',
  `xianyu_user_upgrade` tinyint(1) DEFAULT 0 COMMENT '闲鱼用户升级',
  `zhima_certified` tinyint(1) DEFAULT 0 COMMENT '芝麻认证',
  `tb_xianyu_user` tinyint(1) DEFAULT 0 COMMENT '淘宝闲鱼用户',
  `alibaba_idle_playboy` tinyint(1) DEFAULT 0 COMMENT '阿里巴巴闲置玩家',
  
  -- 店铺信息
  `shop_level` varchar(10) DEFAULT NULL COMMENT '店铺等级(L1-L5)',
  `shop_score` int(11) DEFAULT 0 COMMENT '店铺积分',
  `next_level_need_score` int(11) DEFAULT 0 COMMENT '升级所需积分',
  `praise_ratio` decimal(5,2) DEFAULT 0.00 COMMENT '好评率(%)',
  `review_num` int(11) DEFAULT 0 COMMENT '评价数量',
  `item_topping_limit` int(11) DEFAULT 0 COMMENT '置顶商品限制',
  `show_phone_number` tinyint(1) DEFAULT 0 COMMENT '是否显示手机号',
  `super_show` tinyint(1) DEFAULT 0 COMMENT '超级展示',
  `level_jump_url` text DEFAULT NULL COMMENT '等级跳转URL',
  
  -- 社交信息
  `follow_status` tinyint(4) DEFAULT 0 COMMENT '关注状态(0未关注,1已关注)',
  `followers_count` int(11) DEFAULT 0 COMMENT '粉丝数',
  `following_count` int(11) DEFAULT 0 COMMENT '关注数',
  `attention_privacy_protected` tinyint(1) DEFAULT 0 COMMENT '关注隐私保护',
  
  -- 商品和评价统计
  `item_count` int(11) DEFAULT 0 COMMENT '商品数量',
  `rate_count` int(11) DEFAULT 0 COMMENT '评价数量',
  
  -- 信用等级信息
  `ylz_buyer_level` tinyint(4) DEFAULT 0 COMMENT '买家信用等级',
  `ylz_level_text` varchar(50) DEFAULT NULL COMMENT '信用等级文本',
  `ylz_level_icon` text DEFAULT NULL COMMENT '信用等级图标',
  `ylz_level_lottie` text DEFAULT NULL COMMENT '信用等级动画',
  
  -- 系统字段
  `api_trace_id` varchar(100) DEFAULT NULL COMMENT 'API追踪ID',
  `api_version` varchar(10) DEFAULT '1.0' COMMENT 'API版本',
  `raw_data` json DEFAULT NULL COMMENT '原始JSON数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_display_name` (`display_name`),
  KEY `idx_shop_level` (`shop_level`),
  KEY `idx_ip_location` (`ip_location`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='闲鱼用户信息表';

-- 插入示例数据的SQL
INSERT INTO `xianyu_users` (
  `user_id`, `encrypted_user_id`, `display_name`, `avatar_url`, `user_type`, `ip_location`,
  `real_name_certified`, `xianyu_user_upgrade`, `zhima_certified`, `tb_xianyu_user`,
  `shop_level`, `shop_score`, `next_level_need_score`, `praise_ratio`, `review_num`, `item_topping_limit`,
  `show_phone_number`, `super_show`, `follow_status`, `followers_count`, `following_count`,
  `item_count`, `rate_count`, `ylz_buyer_level`, `ylz_level_text`, `api_trace_id`, `api_version`
) VALUES (
  '2201292691262', 
  'YfsR5WklbVFSS+YSSTKwSQ==', 
  'Auroral', 
  'http://img.alicdn.com/bao/uploaded/i2/O1CN013ih5aP1LC2u9RBMcs_!!4611686018427385662-0-mtopupload.jpg',
  1, 
  '江苏省',
  1, 1, 0, 1,
  'L3', 64, 436, 100.00, 14, 1,
  0, 1, 1, 6, 0,
  2, 15, 4, '买家信用优秀', 
  '2150433017538735665333936e1dce', 
  '1.0'
);
