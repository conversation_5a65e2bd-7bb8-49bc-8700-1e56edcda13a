#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XianYuApis 项目统一入口 - 简化版本
集成了浏览器管理、定时任务调度和闲鱼API功能

Author: AI Assistant
Date: 2025-07-30
"""

import asyncio
import os
from loguru import logger

# 导入核心模块
from app_manager import initialize_app, cleanup_app, get_app_status
from tasks import register_all_tasks

# ================================
# 配置区域 - 请在这里修改您的设置
# ================================

# 如果您的任务需要Cookie，请在任务内部自行获取
# XIANYU_COOKIES = ""  # 当前测试任务不需要Cookie

# 应用配置
APP_CONFIG = {
    'headless': False,                    # 是否使用无头浏览器
    'enable_scheduler': True,             # 是否启用定时任务
    'auto_start_scheduler': True,         # 是否自动启动调度器
    'init_goofish_monitor': True,        # 启用闲鱼监控器
    'auto_start_monitor': True,           # 自动启动监控
}

# 测试配置
TEST_MODE = True                          # 是否立即执行任务进行测试


# ================================
# 任务现在在 tasks/ 文件夹中定义
# 您可以在 tasks/ 文件夹中添加新的任务文件
# ================================


# ================================
# 主程序
# ================================

async def main():
    """主函数 - 超简单版本"""

    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    logger.add("logs/xianyu_app_{time}.log", rotation="1 day", retention="7 days")

    print("🐟 XianYuApis - 闲鱼第三方API集成库")
    print("=" * 50)

    try:
        # 1. 初始化应用
        logger.info("🚀 正在初始化应用...")
        app = await initialize_app(APP_CONFIG)
        logger.success("✅ 应用初始化完成")

        # 2. 注册所有任务
        logger.info("📋 正在注册定时任务...")
        register_all_tasks(app.scheduler, TEST_MODE)

        # 3. 运行应用
        logger.info("🎯 应用开始运行，按Ctrl+C退出...")

        while True:
            # 显示状态
            status = get_app_status()
            logger.info(f"📊 状态: 调度器运行={status.get('scheduler_active', False)}, "
                      f"任务数量={status.get('scheduler_tasks', 0)}")

            await asyncio.sleep(60)  # 每分钟显示一次状态

    except KeyboardInterrupt:
        logger.info("📝 收到退出信号，正在清理资源...")
    except Exception as e:
        logger.error(f"❌ 应用运行出错: {e}")
    finally:
        # 4. 清理资源
        await cleanup_app()
        logger.success("✅ 应用已退出")


if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
