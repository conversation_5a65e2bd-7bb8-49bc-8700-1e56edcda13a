#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本
用于验证MySQL连接和基础功能

Author: AI Assistant
Date: 2025-07-30
"""

import asyncio
import json
from loguru import logger
from db.mysql_manager import AsyncMySQLManager
from models.xianyu_user import XianyuUser
from config.database import validate_config, get_database_config


async def test_database_connection():
    """测试数据库连接"""
    logger.info("=== 数据库连接测试 ===")
    
    # 1. 验证配置
    logger.info("1. 验证数据库配置...")
    if not validate_config():
        logger.error("数据库配置无效")
        return False
    
    config = get_database_config()
    logger.info(f"数据库配置: {config['host']}:{config['port']}/{config['db']}")
    
    # 2. 测试连接
    logger.info("2. 测试数据库连接...")
    manager = AsyncMySQLManager()
    
    try:
        success = await manager.initialize()
        if not success:
            logger.error("数据库连接初始化失败")
            return False
        
        logger.success("数据库连接成功")
        
        # 3. 测试基础查询
        logger.info("3. 测试基础查询...")
        result = await manager.execute_query("SELECT NOW() as current_time, VERSION() as version")
        logger.info(f"数据库时间: {result[0]['current_time']}")
        logger.info(f"数据库版本: {result[0]['version']}")
        
        # 4. 检查表是否存在
        logger.info("4. 检查用户表是否存在...")
        table_exists = await manager.table_exists('xianyu_users')
        logger.info(f"xianyu_users表存在: {table_exists}")
        
        if table_exists:
            # 5. 查询表结构
            logger.info("5. 查询表结构...")
            table_info = await manager.get_table_info('xianyu_users')
            logger.info(f"表字段数量: {len(table_info)}")
            
            # 6. 统计用户数量
            logger.info("6. 统计用户数量...")
            user_count = await XianyuUser.count_all()
            logger.info(f"当前用户数量: {user_count}")
        
        await manager.close()
        logger.success("数据库测试完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库测试失败: {e}")
        await manager.close()
        return False


async def test_user_model():
    """测试用户模型"""
    logger.info("=== 用户模型测试 ===")
    
    # 模拟API响应数据
    mock_api_data = {
        'api': 'mtop.idle.web.user.page.head',
        'data': {
            'baseInfo': {
                'encryptedUserId': 'YfsR5WklbVFSS+YSSTKwSQ==',
                'iosVerify': False,
                'kcUserId': '2201292691262',
                'self': False,
                'tags': {
                    'real_name_certification_77': True,
                    'usertag12#576460752303423488': False,
                    'xianyu_user_upgrade': True,
                    'idle_zhima_zheng': False,
                    'tb_xianyu_user': True,
                    'alibaba_idle_playboy': False
                },
                'userType': 1
            },
            'module': {
                'shop': {
                    'level': 'L3',
                    'showPhoneNumber': False,
                    'nextLevelNeedScore': 436,
                    'praiseRatio': 100,
                    'reviewNum': 14,
                    'levelJumpUrl': 'https://h5.m.goofish.com/wow/moyu/moyu-project/fish-pro-workbench/pages/ShopLevel',
                    'superShow': True,
                    'score': 64,
                    'itemToppingLimit': 1
                },
                'social': {
                    'followStatus': 1,
                    'followers': '6',
                    'following': '0',
                    'attentionPrivacyProtected': 'false'
                },
                'tabs': {
                    'item': {'number': 2, 'name': '宝贝'},
                    'rate': {'number': '15', 'name': '信用及评价'}
                },
                'base': {
                    'ipLocation': '江苏省',
                    'displayName': 'Auroral',
                    'avatar': {
                        'avatar': 'http://img.alicdn.com/bao/uploaded/i2/O1CN013ih5aP1LC2u9RBMcs_!!4611686018427385662-0-mtopupload.jpg'
                    },
                    'ylzTags': [{
                        'attributes': {'role': 'buyer', 'level': 4},
                        'code': 'cs_buyer_level',
                        'icon': 'https://gw.alicdn.com/imgextra/i3/O1CN01NJ9BJu1Zb94RpHWYx_!!6000000003212-2-tps-318-66.png',
                        'lottie': 'https://g.alicdn.com/eva-assets/8752fe6bf160bfd113faffa22650b03b/0.0.1/tmp/3436cea/e20c4a0f-d809-4438-b99c-fae76bb5ac86.json',
                        'text': '买家信用优秀',
                        'type': 'ylzLevel',
                        'url': ''
                    }]
                }
            }
        },
        'ret': ['SUCCESS::调用成功'],
        'traceId': '2150433017538735665333936e1dce',
        'v': '1.0'
    }
    
    try:
        # 1. 创建用户模型
        logger.info("1. 从API数据创建用户模型...")
        user = XianyuUser.from_api_response(mock_api_data)
        logger.info(f"用户模型创建成功: {user}")
        
        # 2. 显示解析的数据
        logger.info("2. 解析的用户数据:")
        user_data = user.to_dict()
        for key, value in user_data.items():
            if key != 'raw_data':  # 跳过原始数据显示
                logger.info(f"  {key}: {value}")
        
        # 3. 保存到数据库
        logger.info("3. 保存用户到数据库...")
        user_id = await user.save()
        logger.success(f"用户保存成功，数据库ID: {user_id}")
        
        # 4. 从数据库查询用户
        logger.info("4. 从数据库查询用户...")
        found_user = await XianyuUser.find_by_user_id('2201292691262')
        if found_user:
            logger.success(f"用户查询成功: {found_user.data.get('display_name')}")
        else:
            logger.warning("用户查询失败")
        
        # 5. 统计用户数量
        logger.info("5. 统计用户数量...")
        total_count = await XianyuUser.count_all()
        logger.info(f"数据库中总用户数: {total_count}")
        
        logger.success("用户模型测试完成")
        return True
        
    except Exception as e:
        logger.error(f"用户模型测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("开始数据库集成测试")
    
    # 测试数据库连接
    db_success = await test_database_connection()
    
    if db_success:
        # 测试用户模型
        model_success = await test_user_model()
        
        if model_success:
            logger.success("🎉 所有测试通过！数据库集成成功")
        else:
            logger.error("❌ 用户模型测试失败")
    else:
        logger.error("❌ 数据库连接测试失败")


if __name__ == "__main__":
    asyncio.run(main())
