#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务模块 - 统一导出所有任务类

Author: AI Assistant
Date: 2025-07-30
"""

from .test_task import TestTask
from .xianyu_users_task import XianYuUsersTask

# 导出所有任务类
__all__ = [
    'TestTask',
    'get_all_tasks',
    'get_default_tasks'
]



def get_all_tasks():
    """
    获取所有可用任务的列表

    Returns:
        list: 任务配置列表，格式为 [(task_instance, cron_expr, enabled), ...]
    """
    tasks = [
        # (任务实例, cron表达式, 是否启用)
        (TestTask("test_task"), "0 * * * *", True),  # 每分钟执行一次
        (XianYuUsersTask("xianyu_users_task"), "0 * * * *", True),  # 每分钟执行一次
    ]

    return tasks


def get_default_tasks():
    """
    获取默认启用的任务列表

    Returns:
        list: 默认任务配置列表
    """
    all_tasks = get_all_tasks()
    # 只返回启用的任务
    return [(task, cron, enabled) for task, cron, enabled in all_tasks if enabled]


def register_all_tasks(scheduler, test_mode: bool = False):
    """
    向调度器注册所有任务的便捷函数

    Args:
        scheduler: 调度器实例
        test_mode: 是否立即执行任务进行测试
    """
    tasks = get_default_tasks()

    for task, cron_expr, enabled in tasks:
        if enabled:
            scheduler.add_task(task, cron_expr, execute_immediately=test_mode)
            print(f"✅ 已注册任务: {task.name} ({cron_expr})")

    print(f"📋 共注册了 {len(tasks)} 个任务")
