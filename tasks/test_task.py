#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务 - 最简单的测试任务

Author: AI Assistant
Date: 2025-07-30
"""

from scheduler import BaseTask
from datetime import datetime


class TestTask(BaseTask):
    """
    简单的测试任务
    
    只做一件事：打印"测试任务"
    """
    
    def __init__(self, task_id: str):
        super().__init__(
            task_id=task_id,
            name="测试任务",
            description="最简单的测试任务，只打印一条消息"
        )
        self.execution_count = 0
    
    async def execute(self):
        """
        异步执行测试任务

        Returns:
            dict: 执行结果
        """
        self.execution_count += 1
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 核心功能：打印测试消息
        print("测试任务")

        # 返回执行结果
        result = {
            'status': 'success',
            'message': '测试任务执行完成',
            'execution_time': current_time,
            'execution_count': self.execution_count
        }

        return result
    
    def on_success(self, result):
        """任务成功回调"""
        execution_count = result.get('execution_count', 0)
        print(f"✅ 测试任务执行成功 (第{execution_count}次)")
    
    def on_error(self, error):
        """任务失败回调"""
        print(f"❌ 测试任务执行失败: {error}")
